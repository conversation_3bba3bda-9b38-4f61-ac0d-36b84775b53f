"""
Main Dashboard Application

Entry point for the dashboard web application with FastAPI integration.
"""

import async<PERSON>
import uvicorn
from typing import Dict, Any, Optional
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from data_collection.config import Config as BaseConfig
from data_collection.error_handling.logger import get_logger

from .config import DashboardConfig
from .factories import DashboardOrchestrator, DashboardType
from .web_server import FastAPIServer, WebSocketHandler


class DashboardApp:
    """
    Main dashboard application class.
    
    Coordinates the FastAPI server, WebSocket handler, and all dashboard
    components to provide a complete web-based trading dashboard.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize dashboard application.
        
        Args:
            config_path: Path to configuration file
        """
        self.logger = get_logger(self.__class__.__name__)
        
        # Load configurations
        self.base_config = BaseConfig(config_path)
        self.dashboard_config = self._load_dashboard_config(config_path)
        
        # Initialize orchestrator
        self.orchestrator = DashboardOrchestrator(self.dashboard_config)
        
        # Application components
        self.fastapi_server = None
        self.websocket_handler = None
        self.app = None
        
        # Application state
        self.is_running = False
    
    def _load_dashboard_config(self, config_path: Optional[str]) -> DashboardConfig:
        """Load dashboard configuration."""
        try:
            if config_path:
                dashboard_config_path = Path(config_path).parent / "dashboard_config.yaml"
            else:
                dashboard_config_path = Path("config/dashboard_config.yaml")
            
            if dashboard_config_path.exists():
                return DashboardConfig.from_yaml(str(dashboard_config_path), self.base_config)
            else:
                self.logger.info("Dashboard config not found, using defaults")
                return DashboardConfig(base_config=self.base_config)
                
        except Exception as e:
            self.logger.warning(f"Error loading dashboard config: {str(e)}, using defaults")
            return DashboardConfig(base_config=self.base_config)
    
    def initialize(self) -> bool:
        """Initialize the dashboard application."""
        try:
            self.logger.info("Initializing dashboard application...")
            
            # Initialize orchestrator components
            if not self.orchestrator.initialize_components():
                self.logger.error("Failed to initialize orchestrator components")
                return False
            
            # Create FastAPI server
            self.fastapi_server = FastAPIServer(
                self.dashboard_config,
                data_providers=self.orchestrator.data_providers,
                renderers=self.orchestrator.renderers
            )
            
            if not self.fastapi_server.initialize():
                self.logger.error("Failed to initialize FastAPI server")
                return False
            
            # Get the FastAPI app instance
            self.app = self.fastapi_server.app
            
            # Create WebSocket handler with app reference
            self.websocket_handler = WebSocketHandler(
                self.dashboard_config,
                app=self.app
            )
            
            if not self.websocket_handler.initialize():
                self.logger.error("Failed to initialize WebSocket handler")
                return False
            
            self.logger.info("✓ Dashboard application initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize dashboard application: {str(e)}")
            return False
    
    def start(self) -> bool:
        """Start the dashboard application."""
        try:
            if not self.initialize():
                return False
            
            self.logger.info("Starting dashboard application...")
            
            # Start orchestrator
            if not self.orchestrator.start_dashboard():
                self.logger.error("Failed to start dashboard orchestrator")
                return False
            
            # Start FastAPI server
            if not self.fastapi_server.start():
                self.logger.error("Failed to start FastAPI server")
                return False
            
            # Start WebSocket handler
            if not self.websocket_handler.start():
                self.logger.error("Failed to start WebSocket handler")
                return False
            
            self.is_running = True
            
            self.logger.info(f"🚀 Dashboard application started successfully!")
            self.logger.info(f"   URL: http://{self.dashboard_config.host}:{self.dashboard_config.port}")
            self.logger.info(f"   Theme: {self.dashboard_config.theme}")
            self.logger.info(f"   Update Interval: {self.dashboard_config.update_interval_seconds}s")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start dashboard application: {str(e)}")
            return False
    
    def stop(self) -> bool:
        """Stop the dashboard application."""
        try:
            self.logger.info("Stopping dashboard application...")
            
            # Stop components
            if self.websocket_handler:
                self.websocket_handler.stop()
            
            if self.fastapi_server:
                self.fastapi_server.stop()
            
            if self.orchestrator:
                self.orchestrator.stop_dashboard()
            
            self.is_running = False
            self.logger.info("Dashboard application stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping dashboard application: {str(e)}")
            return False
    
    def run(self):
        """Run the dashboard application with uvicorn."""
        try:
            if not self.initialize():
                raise RuntimeError("Failed to initialize dashboard application")
            
            # Configure uvicorn
            uvicorn_config = uvicorn.Config(
                app=self.app,
                host=self.dashboard_config.host,
                port=self.dashboard_config.port,
                log_level="info" if self.dashboard_config.debug else "warning",
                reload=self.dashboard_config.auto_reload and self.dashboard_config.debug,
                workers=1,  # Single worker for WebSocket support
                access_log=self.dashboard_config.debug
            )
            
            # Create and run server
            server = uvicorn.Server(uvicorn_config)
            
            self.logger.info(f"🚀 Starting dashboard server on http://{self.dashboard_config.host}:{self.dashboard_config.port}")
            
            # Start background tasks
            self._start_background_tasks()
            
            # Run server
            server.run()
            
        except KeyboardInterrupt:
            self.logger.info("Dashboard application interrupted by user")
        except Exception as e:
            self.logger.error(f"Error running dashboard application: {str(e)}")
            raise
        finally:
            self.stop()
    
    def _start_background_tasks(self):
        """Start background tasks for data updates."""
        # This will be implemented to start periodic data updates
        # and WebSocket broadcasting
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """Get application status."""
        return {
            'is_running': self.is_running,
            'config': {
                'host': self.dashboard_config.host,
                'port': self.dashboard_config.port,
                'theme': self.dashboard_config.theme,
                'debug': self.dashboard_config.debug
            },
            'components': {
                'orchestrator': self.orchestrator.get_system_status() if self.orchestrator else None,
                'fastapi_server': self.fastapi_server.get_status() if self.fastapi_server else None,
                'websocket_handler': self.websocket_handler.get_status() if self.websocket_handler else None
            }
        }


def create_app(config_path: Optional[str] = None) -> DashboardApp:
    """
    Create dashboard application instance.
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        DashboardApp instance
    """
    return DashboardApp(config_path)


def main():
    """Main entry point for dashboard application."""
    import argparse
    
    parser = argparse.ArgumentParser(description="XAUUSD Trading Dashboard")
    parser.add_argument(
        "--config", "-c",
        type=str,
        help="Path to configuration file"
    )
    parser.add_argument(
        "--host",
        type=str,
        default="127.0.0.1",
        help="Host to bind to"
    )
    parser.add_argument(
        "--port", "-p",
        type=int,
        default=8081,
        help="Port to bind to"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )
    
    args = parser.parse_args()
    
    try:
        # Create application
        app = create_app(args.config)
        
        # Override config with command line arguments
        if args.host:
            app.dashboard_config.host = args.host
        if args.port:
            app.dashboard_config.port = args.port
        if args.debug:
            app.dashboard_config.debug = True
        
        # Run application
        app.run()
        
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
