"""
Dashboard Factory System

Provides factory classes for creating dashboard components following
clean architecture principles and enabling easy extensibility.
"""

from typing import Dict, Any, List, Optional, Type
from abc import ABC, abstractmethod
from enum import Enum
from pathlib import Path
import sys

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.error_handling.logger import LoggerMixin
from data_collection.error_handling.exceptions import ConfigurationError

from .config import DashboardConfig
from .base import BaseDashboardComponent, BaseDashboardDataProvider, BaseDashboardRenderer


class DashboardType(Enum):
    """Enumeration of available dashboard types."""
    WEB_DASHBOARD = "web_dashboard"
    API_DASHBOARD = "api_dashboard"
    REALTIME_DASHBOARD = "realtime_dashboard"
    MONITORING_DASHBOARD = "monitoring_dashboard"


class ComponentType(Enum):
    """Enumeration of dashboard component types."""
    DATA_PROVIDER = "data_provider"
    RENDERER = "renderer"
    WEB_SERVER = "web_server"
    WEBSOCKET_HANDLER = "websocket_handler"
    CHART_GENERATOR = "chart_generator"
    METRICS_CALCULATOR = "metrics_calculator"


class DashboardFactory:
    """
    Factory for creating dashboard components and systems.

    Follows the established factory pattern used throughout the system
    for consistent component creation and configuration.
    """

    def __init__(self, config: DashboardConfig):
        """
        Initialize dashboard factory.

        Args:
            config: Dashboard configuration instance
        """
        self.config = config

        # Import logging from existing system
        from data_collection.error_handling.logger import get_logger
        self.logger = get_logger(self.__class__.__name__)
        
        # Registry of available dashboard types
        self._dashboard_registry: Dict[DashboardType, Type] = {}
        self._component_registry: Dict[ComponentType, Type] = {}
        
        # Initialize registries
        self._register_dashboards()
        self._register_components()
    
    def _register_dashboards(self):
        """Register available dashboard implementations."""
        try:
            # Import dashboard implementations
            from .web import WebDashboard
            from .api import APIDashboard
            from .realtime import RealtimeDashboard
            from .monitoring import MonitoringDashboard
            
            self._dashboard_registry = {
                DashboardType.WEB_DASHBOARD: WebDashboard,
                DashboardType.API_DASHBOARD: APIDashboard,
                DashboardType.REALTIME_DASHBOARD: RealtimeDashboard,
                DashboardType.MONITORING_DASHBOARD: MonitoringDashboard
            }
            
            self.logger.info(f"Registered {len(self._dashboard_registry)} dashboard types")
            
        except ImportError as e:
            self.logger.warning(f"Some dashboard implementations not available: {str(e)}")
            # Register minimal set
            self._dashboard_registry = {}
    
    def _register_components(self):
        """Register available component implementations."""
        try:
            # Import component implementations
            from .data_providers import (
                LiveTradingDataProvider,
                MT5DataProvider,
                ResultsDataProvider,
                ModelDataProvider,
                RealTimePerformanceProvider
            )
            from .renderers import HTMLRenderer, ChartRenderer, MetricsRenderer
            from .web_server import FastAPIServer
            from .websocket_handler import WebSocketHandler
            from .charts import TradingChartGenerator
            from .metrics import DashboardMetricsCalculator

            self._component_registry = {
                ComponentType.DATA_PROVIDER: {
                    'live_trading': LiveTradingDataProvider,
                    'real_time_performance': RealTimePerformanceProvider,
                    'mt5': MT5DataProvider,
                    'results': ResultsDataProvider,
                    'models': ModelDataProvider
                },
                ComponentType.RENDERER: {
                    'html': HTMLRenderer,
                    'charts': ChartRenderer,
                    'metrics': MetricsRenderer
                },
                ComponentType.WEB_SERVER: FastAPIServer,
                ComponentType.WEBSOCKET_HANDLER: WebSocketHandler,
                ComponentType.CHART_GENERATOR: TradingChartGenerator,
                ComponentType.METRICS_CALCULATOR: DashboardMetricsCalculator
            }
            
            self.logger.info(f"Registered {len(self._component_registry)} component types")
            
        except ImportError as e:
            self.logger.warning(f"Some components not available: {str(e)}")
            # Register minimal set
            self._component_registry = {}
    
    def create_dashboard(self, dashboard_type: DashboardType, **kwargs) -> BaseDashboardComponent:
        """
        Create a dashboard instance.
        
        Args:
            dashboard_type: Type of dashboard to create
            **kwargs: Additional parameters for dashboard creation
            
        Returns:
            Dashboard instance
            
        Raises:
            ConfigurationError: If dashboard type not available
        """
        if dashboard_type not in self._dashboard_registry:
            available_types = list(self._dashboard_registry.keys())
            raise ConfigurationError(
                f"Dashboard type {dashboard_type} not available. "
                f"Available types: {available_types}"
            )
        
        dashboard_class = self._dashboard_registry[dashboard_type]
        
        try:
            # Create dashboard with configuration
            dashboard = dashboard_class(self.config, **kwargs)
            self.logger.info(f"Created {dashboard_type.value} dashboard: {dashboard_class.__name__}")
            return dashboard
            
        except Exception as e:
            self.logger.error(f"Failed to create {dashboard_type.value} dashboard: {str(e)}")
            raise
    
    def create_component(self, component_type: ComponentType, 
                        component_name: Optional[str] = None, **kwargs) -> BaseDashboardComponent:
        """
        Create a dashboard component.
        
        Args:
            component_type: Type of component to create
            component_name: Specific component name (for multi-implementation types)
            **kwargs: Additional parameters for component creation
            
        Returns:
            Component instance
            
        Raises:
            ConfigurationError: If component type not available
        """
        if component_type not in self._component_registry:
            available_types = list(self._component_registry.keys())
            raise ConfigurationError(
                f"Component type {component_type} not available. "
                f"Available types: {available_types}"
            )
        
        component_registry = self._component_registry[component_type]
        
        # Handle multi-implementation component types
        if isinstance(component_registry, dict):
            if not component_name:
                # Use first available implementation
                component_name = list(component_registry.keys())[0]
            
            if component_name not in component_registry:
                available_names = list(component_registry.keys())
                raise ConfigurationError(
                    f"Component {component_name} not available for {component_type}. "
                    f"Available: {available_names}"
                )
            
            component_class = component_registry[component_name]
        else:
            component_class = component_registry
        
        try:
            # Create component with configuration
            component = component_class(self.config, **kwargs)
            self.logger.info(f"Created {component_type.value} component: {component_class.__name__}")
            return component
            
        except Exception as e:
            self.logger.error(f"Failed to create {component_type.value} component: {str(e)}")
            raise
    
    def get_available_dashboards(self) -> List[DashboardType]:
        """Get list of available dashboard types."""
        return list(self._dashboard_registry.keys())
    
    def get_available_components(self) -> Dict[ComponentType, Any]:
        """Get dictionary of available component types."""
        return {
            comp_type: (list(impl.keys()) if isinstance(impl, dict) else impl.__name__)
            for comp_type, impl in self._component_registry.items()
        }
    
    def validate_configuration(self) -> List[str]:
        """Validate factory configuration."""
        issues = []
        
        # Validate base configuration
        config_issues = self.config.validate()
        issues.extend(config_issues)
        
        # Check if any dashboards are available
        if not self._dashboard_registry:
            issues.append("No dashboard implementations available")
        
        # Check if essential components are available
        essential_components = [
            ComponentType.DATA_PROVIDER,
            ComponentType.RENDERER,
            ComponentType.WEB_SERVER
        ]
        
        for comp_type in essential_components:
            if comp_type not in self._component_registry:
                issues.append(f"Essential component type not available: {comp_type}")
        
        return issues


class DashboardOrchestrator:
    """
    Orchestrates dashboard operations across multiple components.

    Coordinates data providers, renderers, web servers, and other
    components to provide a complete dashboard system.
    """

    def __init__(self, config: DashboardConfig):
        """
        Initialize dashboard orchestrator.

        Args:
            config: Dashboard configuration instance
        """
        self.config = config

        # Import logging from existing system
        from data_collection.error_handling.logger import get_logger
        self.logger = get_logger(self.__class__.__name__)
        
        # Initialize factory
        self.factory = DashboardFactory(config)
        
        # Component instances
        self.dashboard = None
        self.data_providers = {}
        self.renderers = {}
        self.web_server = None
        self.websocket_handler = None
        
        # Orchestrator state
        self.is_running = False
        self.initialization_errors = []
    
    def initialize_components(self) -> bool:
        """
        Initialize all dashboard components.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            self.logger.info("Initializing dashboard components...")
            
            # Initialize data providers
            self._initialize_data_providers()
            
            # Initialize renderers
            self._initialize_renderers()
            
            # Initialize web server
            self._initialize_web_server()
            
            # Initialize WebSocket handler
            self._initialize_websocket_handler()
            
            # Initialize main dashboard
            self._initialize_dashboard()
            
            self.logger.info("✓ All dashboard components initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize dashboard components: {str(e)}")
            self.initialization_errors.append(str(e))
            return False
    
    def _initialize_data_providers(self):
        """Initialize data provider components."""
        provider_types = ['mt5', 'results', 'models']

        for provider_type in provider_types:
            if self.config.is_data_source_enabled(provider_type):
                try:
                    provider = self.factory.create_component(
                        ComponentType.DATA_PROVIDER,
                        provider_type
                    )
                    if provider.initialize():
                        self.data_providers[provider_type] = provider
                        self.logger.info(f"✓ Initialized {provider_type} data provider")
                    else:
                        self.logger.warning(f"Failed to initialize {provider_type} data provider")
                except Exception as e:
                    self.logger.error(f"Error initializing {provider_type} provider: {str(e)}")

        # Connect data providers to WebSocket handler if available
        if hasattr(self, 'websocket_handler') and self.websocket_handler:
            self.websocket_handler.set_data_providers(self.data_providers)
            self.logger.info("Connected data providers to WebSocket handler")
    
    def _initialize_renderers(self):
        """Initialize renderer components."""
        renderer_types = ['html', 'charts', 'metrics']
        
        for renderer_type in renderer_types:
            try:
                renderer = self.factory.create_component(
                    ComponentType.RENDERER,
                    renderer_type
                )
                if renderer.initialize():
                    self.renderers[renderer_type] = renderer
                    self.logger.info(f"✓ Initialized {renderer_type} renderer")
                else:
                    self.logger.warning(f"Failed to initialize {renderer_type} renderer")
            except Exception as e:
                self.logger.error(f"Error initializing {renderer_type} renderer: {str(e)}")
    
    def _initialize_web_server(self):
        """Initialize web server component."""
        try:
            self.web_server = self.factory.create_component(ComponentType.WEB_SERVER)
            if self.web_server.initialize():
                self.logger.info("✓ Initialized web server")
            else:
                self.logger.error("Failed to initialize web server")
        except Exception as e:
            self.logger.error(f"Error initializing web server: {str(e)}")
    
    def _initialize_websocket_handler(self):
        """Initialize WebSocket handler component."""
        try:
            self.websocket_handler = self.factory.create_component(ComponentType.WEBSOCKET_HANDLER)
            if self.websocket_handler.initialize():
                self.logger.info("✓ Initialized WebSocket handler")
            else:
                self.logger.error("Failed to initialize WebSocket handler")
        except Exception as e:
            self.logger.error(f"Error initializing WebSocket handler: {str(e)}")
    
    def _initialize_dashboard(self):
        """Initialize main dashboard component."""
        try:
            self.dashboard = self.factory.create_dashboard(DashboardType.WEB_DASHBOARD)
            if self.dashboard.initialize():
                self.logger.info("✓ Initialized main dashboard")
            else:
                self.logger.error("Failed to initialize main dashboard")
        except Exception as e:
            self.logger.error(f"Error initializing main dashboard: {str(e)}")
    
    def start_dashboard(self) -> bool:
        """
        Start the dashboard system.
        
        Returns:
            True if start successful, False otherwise
        """
        if not self.initialize_components():
            return False
        
        try:
            # Start all components
            if self.web_server and self.web_server.start():
                self.logger.info("✓ Web server started")
            
            if self.websocket_handler and self.websocket_handler.start():
                self.logger.info("✓ WebSocket handler started")
            
            if self.dashboard and self.dashboard.start():
                self.logger.info("✓ Dashboard started")
            
            self.is_running = True
            self.logger.info(f"🚀 Dashboard system started on http://{self.config.host}:{self.config.port}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start dashboard system: {str(e)}")
            return False
    
    def stop_dashboard(self) -> bool:
        """
        Stop the dashboard system.
        
        Returns:
            True if stop successful, False otherwise
        """
        try:
            # Stop all components
            if self.dashboard:
                self.dashboard.stop()
            
            if self.websocket_handler:
                self.websocket_handler.stop()
            
            if self.web_server:
                self.web_server.stop()
            
            self.is_running = False
            self.logger.info("Dashboard system stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping dashboard system: {str(e)}")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status."""
        return {
            'is_running': self.is_running,
            'components': {
                'data_providers': len(self.data_providers),
                'renderers': len(self.renderers),
                'web_server': self.web_server is not None,
                'websocket_handler': self.websocket_handler is not None,
                'dashboard': self.dashboard is not None
            },
            'errors': self.initialization_errors,
            'config': {
                'host': self.config.host,
                'port': self.config.port,
                'theme': self.config.theme
            }
        }
