/* Professional Trading Dashboard CSS */

:root {
    /* Dark Space Theme Colors */
    --bg-primary: #0a0a0f;
    --bg-secondary: #141420;
    --bg-tertiary: #1e1e2e;
    --bg-card: #252540;
    --bg-hover: #2a2a45;

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #e0e0e0;
    --text-muted: #a0a0b0;
    --text-dim: #707080;

    /* Trading Colors */
    --color-gold: #ffd700;
    --color-profit: #00ff88;
    --color-loss: #ff4444;
    --color-neutral: #888888;

    /* Accent Colors */
    --accent-primary: #00d4aa;
    --accent-secondary: #6c5ce7;
    --accent-tertiary: #fd79a8;

    /* Status Colors */
    --success-color: #00ff88;
    --danger-color: #ff4444;
    --warning-color: #ffaa00;
    --info-color: #00aaff;

    /* Border and Shadow */
    --border-color: #333344;
    --border-light: #444455;
    --shadow-color: rgba(0, 0, 0, 0.5);
    --glow-color: rgba(0, 212, 170, 0.3);

    /* Chart Colors */
    --chart-grid: rgba(255, 255, 255, 0.1);
    --chart-text: #ffffff;
    --chart-line: #00d4aa;
    --chart-fill: rgba(0, 212, 170, 0.1);
}

/* Connection Status Indicator */
.connection-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.connection-status::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.connection-status.connected {
    background: rgba(0, 255, 136, 0.2);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.connection-status.connected::before {
    background: var(--success-color);
    animation: pulse-success 2s infinite;
}

.connection-status.connecting {
    background: rgba(255, 170, 0, 0.2);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.connection-status.connecting::before {
    background: var(--warning-color);
    animation: pulse-warning 1s infinite;
}

.connection-status.error {
    background: rgba(255, 68, 68, 0.2);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.connection-status.error::before {
    background: var(--danger-color);
}

@keyframes pulse-success {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes pulse-warning {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* Status Classes */
.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--accent-primary);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }

    .row {
        margin: 0 -0.25rem;
    }

    .col-12, .col-md-6, .col-lg-3, .col-lg-4, .col-lg-6, .col-lg-8 {
        padding: 0 0.25rem;
        margin-bottom: 0.5rem;
    }

    .trading-card {
        padding: 1rem;
        margin-bottom: 0.5rem;
    }

    .price-display {
        font-size: 1.5rem !important;
    }

    .d-flex.align-items-center {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .me-4 {
        margin-right: 1rem !important;
        margin-bottom: 0.5rem;
    }

    .chart-container {
        height: 250px !important;
    }

    .metrics-grid {
        grid-template-columns: 1fr 1fr !important;
        gap: 0.5rem;
    }

    .metric-card {
        padding: 0.75rem;
    }

    .metric-value {
        font-size: 1.25rem !important;
    }

    .metric-label {
        font-size: 0.75rem !important;
    }
}

@media (max-width: 576px) {
    .price-display {
        font-size: 1.25rem !important;
    }

    .metrics-grid {
        grid-template-columns: 1fr !important;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
    }

    .trading-card h2 {
        font-size: 1.25rem !important;
    }

    .chart-container {
        height: 200px !important;
    }
}

/* Professional Theme Consistency */

/* Ensure all cards follow the professional theme */
.card {
    background: linear-gradient(145deg, var(--bg-card) 0%, var(--bg-tertiary) 100%) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px var(--shadow-color) !important;
    color: var(--text-primary) !important;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px var(--shadow-color) !important;
    border-color: var(--accent-primary) !important;
}

.card-header {
    background: linear-gradient(90deg, var(--bg-tertiary), var(--bg-card)) !important;
    border-bottom: 2px solid var(--border-color) !important;
    color: var(--text-secondary) !important;
    font-weight: 600;
}

.card-body {
    color: var(--text-primary) !important;
}

/* Professional table styling */
.table {
    background: var(--bg-card) !important;
    color: var(--text-primary) !important;
    border-radius: 12px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(90deg, var(--bg-tertiary), var(--bg-card)) !important;
    color: var(--text-secondary) !important;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 1px;
    border-bottom: 2px solid var(--border-color) !important;
}

.table tbody tr {
    border-bottom: 1px solid var(--border-light) !important;
}

.table tbody tr:hover {
    background: var(--bg-hover) !important;
}

.table tbody td {
    color: var(--text-primary) !important;
    border-color: var(--border-light) !important;
}

/* Professional badges */
.badge {
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.badge.bg-success {
    background: linear-gradient(45deg, var(--success-color), #00cc77) !important;
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}

.badge.bg-danger {
    background: linear-gradient(45deg, var(--danger-color), #cc3333) !important;
    box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);
}

.badge.bg-warning {
    background: linear-gradient(45deg, var(--warning-color), #ff9900) !important;
    box-shadow: 0 0 10px rgba(255, 170, 0, 0.3);
}

.badge.bg-info {
    background: linear-gradient(45deg, var(--info-color), #0088cc) !important;
    box-shadow: 0 0 10px rgba(0, 170, 255, 0.3);
}

[data-theme="dark"] .btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

[data-theme="dark"] .btn-outline-primary:hover,
[data-theme="dark"] .btn-outline-primary.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

[data-theme="dark"] .progress {
    background-color: var(--bg-darker);
}

[data-theme="dark"] .border-end {
    border-right-color: var(--border-color) !important;
}

/* Layout */
.dashboard-body {
    padding-top: 76px; /* Account for fixed navbar */
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    flex: 1;
    padding: 20px 0;
}

.footer {
    margin-top: auto;
}

/* Cards */
.card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-title {
    font-weight: 600;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-online {
    background-color: var(--success-color);
    animation: pulse 2s infinite;
}

.status-offline {
    background-color: var(--danger-color);
}

.status-warning {
    background-color: var(--warning-color);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Progress bars */
.progress-sm {
    height: 8px;
}

.progress-xs {
    height: 4px;
}

/* Charts */
.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
}

/* Tables */
.table-sm th,
.table-sm td {
    padding: 0.5rem;
    font-size: 0.875rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .main-content {
        padding: 15px 0;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .h5 {
        font-size: 1.1rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

@media (max-width: 576px) {
    .dashboard-body {
        padding-top: 66px;
    }
    
    .main-content {
        padding: 10px 0;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
}

/* Custom components */
.metric-card {
    text-align: center;
    padding: 1.5rem;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.trading-signal {
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.signal-buy {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid var(--success-color);
}

.signal-sell {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 4px solid var(--danger-color);
}

.signal-hold {
    background-color: rgba(255, 193, 7, 0.1);
    border-left: 4px solid var(--warning-color);
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Utility classes */
.text-profit {
    color: var(--success-color) !important;
}

.text-loss {
    color: var(--danger-color) !important;
}

.bg-profit {
    background-color: rgba(40, 167, 69, 0.1) !important;
}

.bg-loss {
    background-color: rgba(220, 53, 69, 0.1) !important;
}

.border-profit {
    border-color: var(--success-color) !important;
}

.border-loss {
    border-color: var(--danger-color) !important;
}

/* Space theme enhancements */
.space-gradient {
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
}

.nebula-effect {
    background: radial-gradient(ellipse at center, rgba(0, 123, 255, 0.1) 0%, transparent 70%);
}

.star-field::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: stars 20s linear infinite;
    pointer-events: none;
}

@keyframes stars {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-200px);
    }
}

/* Professional Trading Dashboard Enhancements */

/* Enhanced Body Styling */
body {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%) !important;
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif !important;
}

/* Professional Trading Cards */
.trading-card {
    background: linear-gradient(145deg, var(--bg-card) 0%, var(--bg-tertiary) 100%);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 20px var(--shadow-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.trading-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
}

.trading-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px var(--shadow-color);
    border-color: var(--accent-primary);
}

/* Professional Price Display */
.price-display {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-gold);
    text-shadow: 0 0 20px var(--glow-color);
    margin: 0;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
}

.price-change {
    font-size: 1.2rem;
    font-weight: 600;
    margin-left: 1rem;
}

.price-change.positive {
    color: var(--color-profit);
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.price-change.negative {
    color: var(--color-loss);
    text-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
}

/* Enhanced Navigation */
.navbar {
    background: linear-gradient(90deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%) !important;
    border-bottom: 2px solid var(--border-color) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px var(--shadow-color) !important;
}

.navbar-brand {
    color: var(--color-gold) !important;
    font-weight: 700 !important;
    font-size: 1.5rem !important;
    text-shadow: 0 0 10px var(--glow-color);
}

/* Professional Tables */
.table {
    background: var(--bg-card);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px var(--shadow-color);
}

.table thead th {
    background: linear-gradient(90deg, var(--bg-tertiary), var(--bg-card));
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 1px;
    border-bottom: 2px solid var(--border-color);
}

.table tbody tr:hover {
    background: var(--bg-hover);
}

/* Enhanced Connection Status */
.connection-status {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background: linear-gradient(45deg, var(--success-color), #00cc77);
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
}

.connection-status.disconnected {
    background: linear-gradient(45deg, var(--warning-color), #ff9900);
    box-shadow: 0 0 15px rgba(255, 170, 0, 0.3);
}

.connection-status.error {
    background: linear-gradient(45deg, var(--danger-color), #cc3333);
    box-shadow: 0 0 15px rgba(255, 68, 68, 0.3);
}

/* Professional Metric Cards */
.metric-card {
    background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-primary);
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px var(--shadow-color);
}

.metric-value {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
}

.metric-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

/* Professional Trading Colors */
.text-gold { color: var(--color-gold) !important; }
.text-profit {
    color: var(--color-profit) !important;
    text-shadow: 0 0 5px rgba(0, 255, 136, 0.3);
}
.text-loss {
    color: var(--color-loss) !important;
    text-shadow: 0 0 5px rgba(255, 68, 68, 0.3);
}

/* Professional Responsive Design Improvements */

/* Tablet View (768px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .navbar-collapse {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: linear-gradient(90deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
        border-top: 1px solid var(--border-color);
        box-shadow: 0 4px 20px var(--shadow-color);
        z-index: 1000;
    }

    .navbar-nav {
        padding: 1rem;
    }

    .nav-link {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--border-light);
    }

    .nav-link:last-child {
        border-bottom: none;
    }

    .trading-card {
        padding: 1.25rem;
        margin-bottom: 1rem;
    }

    .price-display {
        font-size: 2.2rem;
    }

    .metric-card {
        padding: 1.25rem;
        margin-bottom: 1rem;
    }
}

/* Mobile View (max-width: 768px) */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }

    .navbar-collapse {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: linear-gradient(90deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
        border-top: 1px solid var(--border-color);
        box-shadow: 0 4px 20px var(--shadow-color);
        z-index: 1000;
        max-height: 70vh;
        overflow-y: auto;
    }

    .navbar-nav {
        padding: 1rem;
    }

    .nav-link {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--border-light);
        font-size: 1rem;
    }

    .trading-card {
        margin-bottom: 15px;
        padding: 1rem;
    }

    .trading-card .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .trading-card .d-flex > div:first-child {
        margin-bottom: 1rem;
    }

    .navbar-brand {
        font-size: 1.2rem !important;
    }

    .price-display {
        font-size: 2rem;
    }

    .metric-card {
        padding: 1rem;
        margin-bottom: 1rem;
        text-align: center;
    }

    .metric-value {
        font-size: 1.8rem;
    }

    .table-responsive {
        font-size: 0.9rem;
    }

    /* Ensure content doesn't get hidden behind navbar */
    .main-content {
        padding-top: 1rem;
    }
}

/* Small Mobile View (max-width: 576px) */
@media (max-width: 576px) {
    .price-display {
        font-size: 1.8rem;
    }

    .metric-value {
        font-size: 1.6rem;
    }

    .trading-card {
        padding: 0.75rem;
    }

    .metric-card {
        padding: 0.75rem;
    }

    .navbar-brand {
        font-size: 1.1rem !important;
    }

    .table-responsive {
        font-size: 0.8rem;
    }
}
