{% extends "base.html" %}

{% block title %}Live Trading - {{ super() }}{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-nasa-light mb-2">
                <i class="fas fa-exchange-alt text-nasa-accent mr-3"></i>
                Live Trading
            </h1>
            <p class="text-nasa-gray">Real-time trading operations and positions</p>
        </div>
        <div class="flex items-center mt-4 sm:mt-0 space-x-3">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-nasa-accent/20 text-nasa-accent">
                <i class="fas fa-circle animate-pulse mr-2"></i>
                Trading Active
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-500/20 text-blue-400">
                XAUUSD
            </span>
        </div>
    </div>
</div>

<!-- Account Summary Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Account Balance Card -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-nasa-accent/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-wallet text-nasa-accent text-xl"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Account Balance</h3>
                    <p class="text-2xl font-bold text-nasa-accent" id="account-balance">$10,245.67</p>
                </div>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-coins mr-2"></i>
            Available: <span id="available-margin" class="text-nasa-light ml-1">$8,156.23</span>
        </div>
    </div>

    <!-- Daily P&L Card -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-nasa-accent/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-chart-line text-nasa-accent text-xl"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Daily P&L</h3>
                    <p class="text-2xl font-bold text-nasa-accent" id="daily-pnl">+$125.45</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                    +1.23%
                </span>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-calendar mr-2"></i>
            Today's Performance
        </div>
    </div>

    <!-- Open Positions Card -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-chart-area text-blue-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Open Positions</h3>
                    <p class="text-2xl font-bold text-nasa-light" id="open-positions">3</p>
                </div>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-dollar-sign mr-2"></i>
            Exposure: <span id="total-exposure" class="text-nasa-light ml-1">$15,420</span>
        </div>
    </div>

    <!-- Win Rate Card -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-percentage text-purple-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Win Rate Today</h3>
                    <p class="text-2xl font-bold text-nasa-light" id="daily-winrate">75.0%</p>
                </div>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-chart-pie mr-2"></i>
            12 trades completed
        </div>
    </div>
</div>

<!-- Live Price and Chart Section -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Live PnL Chart -->
    <div class="lg:col-span-2 chart-container">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-chart-line text-nasa-accent mr-2"></i>
                Live P&L Chart
            </h3>
            <div class="flex space-x-2">
                <button class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-accent/20 text-nasa-accent">1H</button>
                <button class="px-3 py-1 text-xs font-medium rounded-lg text-nasa-gray hover:bg-nasa-gray/10">4H</button>
                <button class="px-3 py-1 text-xs font-medium rounded-lg text-nasa-gray hover:bg-nasa-gray/10">1D</button>
            </div>
        </div>
        <div class="h-80">
            <canvas id="live-pnl-chart"></canvas>
        </div>
    </div>

    <!-- Live Price Panel -->
    <div class="trading-card">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-dollar-sign text-nasa-accent mr-2"></i>
                XAUUSD Live Price
            </h3>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                <i class="fas fa-circle animate-pulse mr-1"></i>
                Live
            </span>
        </div>

        <!-- Current Price Display -->
        <div class="text-center mb-6">
            <div class="text-4xl font-bold text-nasa-light mb-2" id="live-price">$2,650.45</div>
            <div class="text-lg text-nasa-accent" id="live-change">+$2.15 (+0.08%)</div>
        </div>

        <!-- Bid/Ask Spread -->
        <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="bg-nasa-dark/50 rounded-xl p-4 text-center">
                <p class="text-sm text-nasa-gray mb-1">Bid</p>
                <p class="text-xl font-semibold text-nasa-red" id="bid-price">2650.23</p>
            </div>
            <div class="bg-nasa-dark/50 rounded-xl p-4 text-center">
                <p class="text-sm text-nasa-gray mb-1">Ask</p>
                <p class="text-xl font-semibold text-nasa-accent" id="ask-price">2650.67</p>
            </div>
        </div>

        <!-- Market Info -->
        <div class="space-y-3">
            <div class="flex justify-between items-center">
                <span class="text-sm text-nasa-gray">Spread</span>
                <span class="text-sm font-medium text-nasa-light" id="spread">0.44</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-nasa-gray">Volume</span>
                <span class="text-sm font-medium text-nasa-light" id="volume">1.2M</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-nasa-gray">Last Update</span>
                <span class="text-sm font-medium text-nasa-light" id="last-update">2s ago</span>
            </div>
        </div>
    </div>
</div>

<!-- Active Trades Table -->
<div class="trading-card mb-8">
    <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-nasa-light">
            <i class="fas fa-list text-nasa-accent mr-2"></i>
            Active Trades
        </h3>
        <div class="flex items-center space-x-3">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400" id="positions-count">
                3 positions
            </span>
            <button class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-red/20 text-nasa-red hover:bg-nasa-red/30 transition-colors" onclick="closeAllPositions()">
                <i class="fas fa-times-circle mr-1"></i>
                Close All
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full">
            <thead>
                <tr class="border-b border-nasa-gray/20">
                    <th class="text-left py-3 px-4 text-sm font-medium text-nasa-gray">Asset/Symbol</th>
                    <th class="text-left py-3 px-4 text-sm font-medium text-nasa-gray">Entry Price & Time</th>
                    <th class="text-left py-3 px-4 text-sm font-medium text-nasa-gray">Current P&L</th>
                    <th class="text-left py-3 px-4 text-sm font-medium text-nasa-gray">Risk Level</th>
                    <th class="text-left py-3 px-4 text-sm font-medium text-nasa-gray">Signal Strength</th>
                    <th class="text-left py-3 px-4 text-sm font-medium text-nasa-gray">Actions</th>
                </tr>
            </thead>
            <tbody id="positions-table" class="divide-y divide-nasa-gray/10">
                <tr class="hover:bg-nasa-gray/5 transition-colors">
                    <td class="py-4 px-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-nasa-accent/20 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-arrow-up text-nasa-accent"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-nasa-light">XAUUSD</p>
                                <p class="text-sm text-nasa-gray">BUY • 0.10 lots</p>
                            </div>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div>
                            <p class="font-medium text-nasa-light">$2,648.25</p>
                            <p class="text-sm text-nasa-gray">14:32:15</p>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div>
                            <p class="font-semibold text-nasa-accent">+$22.00</p>
                            <p class="text-sm text-nasa-accent">+0.83%</p>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                            Low Risk
                        </span>
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex items-center">
                            <div class="w-16 bg-nasa-dark rounded-full h-2 mr-2">
                                <div class="bg-nasa-accent h-2 rounded-full" style="width: 87%"></div>
                            </div>
                            <span class="text-sm font-medium text-nasa-light">87%</span>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-red/20 text-nasa-red hover:bg-nasa-red/30 transition-colors" onclick="closePosition(1)">
                                <i class="fas fa-times mr-1"></i>
                                Close
                            </button>
                            <button class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-gray/20 text-nasa-gray hover:bg-nasa-gray/30 transition-colors" onclick="modifyPosition(1)">
                                <i class="fas fa-edit mr-1"></i>
                                Modify
                            </button>
                        </div>
                    </td>
                </tr>

                <tr class="hover:bg-nasa-gray/5 transition-colors">
                    <td class="py-4 px-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-nasa-red/20 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-arrow-down text-nasa-red"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-nasa-light">XAUUSD</p>
                                <p class="text-sm text-nasa-gray">SELL • 0.05 lots</p>
                            </div>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div>
                            <p class="font-medium text-nasa-light">$2,651.80</p>
                            <p class="text-sm text-nasa-gray">14:28:42</p>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div>
                            <p class="font-semibold text-nasa-accent">+$6.75</p>
                            <p class="text-sm text-nasa-accent">+0.25%</p>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-500">
                            Medium Risk
                        </span>
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex items-center">
                            <div class="w-16 bg-nasa-dark rounded-full h-2 mr-2">
                                <div class="bg-nasa-accent h-2 rounded-full" style="width: 72%"></div>
                            </div>
                            <span class="text-sm font-medium text-nasa-light">72%</span>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-red/20 text-nasa-red hover:bg-nasa-red/30 transition-colors" onclick="closePosition(2)">
                                <i class="fas fa-times mr-1"></i>
                                Close
                            </button>
                            <button class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-gray/20 text-nasa-gray hover:bg-nasa-gray/30 transition-colors" onclick="modifyPosition(2)">
                                <i class="fas fa-edit mr-1"></i>
                                Modify
                            </button>
                        </div>
                    </td>
                </tr>

                <tr class="hover:bg-nasa-gray/5 transition-colors">
                    <td class="py-4 px-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-nasa-accent/20 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-arrow-up text-nasa-accent"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-nasa-light">XAUUSD</p>
                                <p class="text-sm text-nasa-gray">BUY • 0.15 lots</p>
                            </div>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div>
                            <p class="font-medium text-nasa-light">$2,649.10</p>
                            <p class="text-sm text-nasa-gray">14:15:08</p>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div>
                            <p class="font-semibold text-nasa-accent">+$20.25</p>
                            <p class="text-sm text-nasa-accent">+0.51%</p>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                            Low Risk
                        </span>
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex items-center">
                            <div class="w-16 bg-nasa-dark rounded-full h-2 mr-2">
                                <div class="bg-nasa-accent h-2 rounded-full" style="width: 94%"></div>
                            </div>
                            <span class="text-sm font-medium text-nasa-light">94%</span>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-red/20 text-nasa-red hover:bg-nasa-red/30 transition-colors" onclick="closePosition(3)">
                                            <i class="fas fa-times mr-1"></i>
                                Close
                            </button>
                            <button class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-gray/20 text-nasa-gray hover:bg-nasa-gray/30 transition-colors" onclick="modifyPosition(3)">
                                <i class="fas fa-edit mr-1"></i>
                                Modify
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

{% endblock %}

{% block extra_scripts %}
<script>
function initializePage() {
    // Initialize live PnL chart
    initializeLivePnLChart();

    // Start real-time updates
    startLiveTradingUpdates();
}

function initializeLivePnLChart() {
    const ctx = document.getElementById('live-pnl-chart').getContext('2d');
    window.livePnLChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Live P&L',
                data: [],
                borderColor: '#22C55E',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#F8FAFC'
                    }
                }
            },
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'minute'
                    },
                    ticks: {
                        color: '#64748B'
                    },
                    grid: {
                        color: 'rgba(100, 116, 139, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: '#64748B'
                    },
                    grid: {
                        color: 'rgba(100, 116, 139, 0.1)'
                    }
                }
            }
        }
    });
}

function startLiveTradingUpdates() {
    // Update live data every second
    setInterval(updateLiveTradingData, 1000);
}

function updateLiveTradingData() {
    // This will be connected to real WebSocket data
    // Update prices, P&L, positions, etc.
}

function closePosition(positionId) {
    if (confirm('Are you sure you want to close this position?')) {
        console.log('Closing position:', positionId);
        // API call to close position
    }
}

function modifyPosition(positionId) {
    console.log('Modifying position:', positionId);
    // Show modify position dialog
}

function closeAllPositions() {
    if (confirm('Are you sure you want to close ALL positions? This action cannot be undone.')) {
        console.log('Closing all positions...');
        // API call to close all positions
    }
}
</script>
{% endblock %}
