"""
Web Server Components

FastAPI-based web server and WebSocket handler for the dashboard.
"""

import asyncio
import uvicorn
from typing import Dict, Any, Set, Optional
from pathlib import Path
from datetime import datetime
import json

try:
    from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, Request, HTTPException
    from fastapi.staticfiles import StaticFiles
    from fastapi.templating import Jin<PERSON>2<PERSON>emplates
    from fastapi.responses import HTMLResponse, JSONResponse
    from fastapi.middleware.cors import CORSMiddleware
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False

from .base import BaseDashboardComponent, DashboardStatus


class FastAPIServer(BaseDashboardComponent):
    """FastAPI web server implementation."""

    def __init__(self, config, **kwargs):
        """Initialize FastAPI server."""
        super().__init__(config)
        self.kwargs = kwargs
        self.app = None
        self.server = None
        self.templates = None

        # Data providers and renderers will be injected
        self.data_providers = kwargs.get('data_providers', {})
        self.renderers = kwargs.get('renderers', {})
        self.websocket_handler = None

        # Initialize real-time performance provider
        from .data_providers import RealTimePerformanceProvider
        self.real_time_provider = None  # Will be initialized when needed

    def initialize(self) -> bool:
        """Initialize the FastAPI server."""
        try:
            if not FASTAPI_AVAILABLE:
                self.add_error("FastAPI not available. Please install: pip install fastapi uvicorn")
                return False

            self.logger.info("Initializing FastAPI server...")

            # Create FastAPI app
            self.app = FastAPI(
                title=self.config.dashboard_title,
                description="Real-Time Trading Dashboard for XAUUSD AI System",
                version="1.0.0",
                docs_url="/docs" if self.config.debug else None,
                redoc_url="/redoc" if self.config.debug else None
            )

            # Setup CORS
            self._setup_cors()

            # Setup static files and templates
            self._setup_static_files()

            # Setup routes
            self._setup_routes()

            self.status = DashboardStatus.RUNNING
            self.logger.info("✓ FastAPI server initialized successfully")
            return True

        except Exception as e:
            self.add_error(f"Failed to initialize FastAPI server: {str(e)}")
            self.status = DashboardStatus.ERROR
            return False

    def _setup_cors(self):
        """Setup CORS middleware."""
        cors_config = self.config.get_cors_config()

        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=cors_config['allow_origins'],
            allow_credentials=cors_config['allow_credentials'],
            allow_methods=cors_config['allow_methods'],
            allow_headers=cors_config['allow_headers']
        )

    def _setup_static_files(self):
        """Setup static files and templates."""
        # Create directories if they don't exist
        static_path = Path(self.config.static_config['static_path'])
        templates_path = Path(self.config.static_config['templates_path'])

        static_path.mkdir(parents=True, exist_ok=True)
        templates_path.mkdir(parents=True, exist_ok=True)

        # Mount static files
        self.app.mount(
            self.config.static_config['static_url_prefix'],
            StaticFiles(directory=str(static_path)),
            name="static"
        )

        # Setup templates
        self.templates = Jinja2Templates(directory=str(templates_path))

    def _setup_routes(self):
        """Setup API routes."""

        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard_home(request: Request):
            """Main dashboard page."""
            try:
                # Get dashboard data
                dashboard_data = await self._get_dashboard_data()

                return self.templates.TemplateResponse(
                    "dashboard.html",
                    {
                        "request": request,
                        "title": self.config.dashboard_title,
                        "data": dashboard_data,
                        "theme": self.config.theme
                    }
                )
            except Exception as e:
                self.logger.error(f"Error rendering dashboard: {str(e)}")
                return HTMLResponse(
                    content=f"<h1>Dashboard Error</h1><p>{str(e)}</p>",
                    status_code=500
                )

        # Real-time API endpoints
        @self.app.get("/api/real-time-metrics")
        async def get_real_time_metrics():
            """Get real-time trading metrics."""
            try:
                # Initialize provider if needed
                if not self.real_time_provider:
                    from .data_providers import RealTimePerformanceProvider
                    self.real_time_provider = RealTimePerformanceProvider(self.config)
                    self.real_time_provider.initialize()

                metrics = self.real_time_provider.get_real_time_metrics()
                return JSONResponse(content=metrics)
            except Exception as e:
                self.logger.error(f"Error getting real-time metrics: {str(e)}")
                return JSONResponse(
                    content={"error": str(e)},
                    status_code=500
                )

        @self.app.get("/api/historical-performance")
        async def get_historical_performance(days: int = 30):
            """Get historical performance data."""
            try:
                # Initialize provider if needed
                if not self.real_time_provider:
                    from .data_providers import RealTimePerformanceProvider
                    self.real_time_provider = RealTimePerformanceProvider(self.config)
                    self.real_time_provider.initialize()

                historical_data = self.real_time_provider.get_historical_performance(days)
                return JSONResponse(content=historical_data)
            except Exception as e:
                self.logger.error(f"Error getting historical performance: {str(e)}")
                return JSONResponse(
                    content={"error": str(e)},
                    status_code=500
                )

        @self.app.get("/api/system-status")
        async def get_system_status():
            """Get current system status."""
            try:
                from .data_providers import LiveTradingDataProvider

                provider = LiveTradingDataProvider(self.config)
                provider.initialize()

                status = provider.get_live_trading_status()
                health = provider.get_system_health_data()
                models = provider.get_model_performance_data()

                return JSONResponse(content={
                    "live_trading": status,
                    "system_health": health,
                    "model_performance": models,
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                self.logger.error(f"Error getting system status: {str(e)}")
                return JSONResponse(
                    content={"error": str(e)},
                    status_code=500
                )

        # Results page route
        @self.app.get("/results", response_class=HTMLResponse)
        async def results_page(request: Request):
            """Results analysis page."""
            try:
                results_data = await self._get_results_page_data()
                return self.templates.TemplateResponse(
                    "results.html",
                    {
                        "request": request,
                        "title": "Results Analysis",
                        "results_data": results_data,
                        "theme": self.config.theme
                    }
                )
            except Exception as e:
                self.logger.error(f"Error loading results page: {str(e)}")
                return HTMLResponse(
                    content=f"<h1>Results Error</h1><p>{str(e)}</p>",
                    status_code=500
                )

        # System health page route
        @self.app.get("/system", response_class=HTMLResponse)
        async def system_page(request: Request):
            """System health page."""
            try:
                system_data = await self._get_system_data()
                return self.templates.TemplateResponse(
                    "system.html",
                    {
                        "request": request,
                        "title": "System Health",
                        "system_data": system_data,
                        "theme": self.config.theme
                    }
                )
            except Exception as e:
                self.logger.error(f"Error loading system page: {str(e)}")
                return HTMLResponse(
                    content=f"<h1>System Error</h1><p>{str(e)}</p>",
                    status_code=500
                )

        # Live trading page route
        @self.app.get("/livetrading", response_class=HTMLResponse)
        async def livetrading_page(request: Request):
            """Live trading page."""
            try:
                trading_data = await self._get_trading_data()
                return self.templates.TemplateResponse(
                    "livetrading.html",
                    {
                        "request": request,
                        "title": "Live Trading",
                        "trading_data": trading_data,
                        "theme": self.config.theme
                    }
                )
            except Exception as e:
                self.logger.error(f"Error loading live trading page: {str(e)}")
                return HTMLResponse(
                    content=f"<h1>Live Trading Error</h1><p>{str(e)}</p>",
                    status_code=500
                )

        # Performance analytics page route
        @self.app.get("/performance", response_class=HTMLResponse)
        async def performance_page(request: Request):
            """Performance analytics page."""
            try:
                performance_data = await self._get_performance_data()
                return self.templates.TemplateResponse(
                    "performance.html",
                    {
                        "request": request,
                        "title": "Performance Analytics",
                        "performance_data": performance_data,
                        "theme": self.config.theme
                    }
                )
            except Exception as e:
                self.logger.error(f"Error loading performance page: {str(e)}")
                return HTMLResponse(
                    content=f"<h1>Performance Error</h1><p>{str(e)}</p>",
                    status_code=500
                )

        @self.app.get("/api/status")
        async def get_system_status():
            """Get system status."""
            try:
                status_data = await self._get_system_status()
                return JSONResponse(content=status_data)
            except Exception as e:
                self.logger.error(f"Error getting system status: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/metrics")
        async def get_metrics():
            """Get trading metrics."""
            try:
                metrics_data = await self._get_metrics_data()
                return JSONResponse(content=metrics_data)
            except Exception as e:
                self.logger.error(f"Error getting metrics: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/positions")
        async def get_positions():
            """Get current positions."""
            try:
                positions_data = await self._get_positions_data()
                return JSONResponse(content=positions_data)
            except Exception as e:
                self.logger.error(f"Error getting positions: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/signals")
        async def get_recent_signals():
            """Get recent trading signals."""
            try:
                signals_data = await self._get_signals_data()
                return JSONResponse(content=signals_data)
            except Exception as e:
                self.logger.error(f"Error getting signals: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/results/{result_type}")
        async def get_results(result_type: str):
            """Get backtest or forward test results."""
            try:
                if result_type not in ['backtest', 'forward_test']:
                    raise HTTPException(status_code=400, detail="Invalid result type")

                results_data = await self._get_results_data(result_type)
                return JSONResponse(content=results_data)
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Error getting {result_type} results: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))

    async def _get_dashboard_data(self) -> Dict[str, Any]:
        """Get data for main dashboard."""
        return {
            'timestamp': datetime.now().isoformat(),
            'system_status': 'running',
            'last_update': datetime.now().isoformat()
        }

    async def _get_results_page_data(self) -> Dict[str, Any]:
        """Get results data for results page."""
        try:
            results_data = {
                'backtest_results': [],
                'forward_test_results': [],
                'summary_stats': {}
            }

            # Get results from data providers
            if hasattr(self, 'data_providers') and 'results_provider' in self.data_providers:
                provider = self.data_providers['results_provider']
                results_data = provider.get_data()

            return results_data

        except Exception as e:
            self.logger.error(f"Error getting results data: {str(e)}")
            return {'error': str(e)}

    async def _get_system_data(self) -> Dict[str, Any]:
        """Get system data for system page."""
        try:
            system_data = {
                'mt5_status': 'Unknown',
                'model_status': 'Unknown',
                'system_health': {},
                'service_status': {}
            }

            # Get system data from providers
            if hasattr(self, 'data_providers'):
                if 'mt5_provider' in self.data_providers:
                    mt5_data = self.data_providers['mt5_provider'].get_data()
                    system_data['mt5_status'] = mt5_data.get('status', 'Unknown')

                if 'model_provider' in self.data_providers:
                    model_data = self.data_providers['model_provider'].get_data()
                    system_data['model_status'] = model_data.get('status', 'Unknown')

            return system_data

        except Exception as e:
            self.logger.error(f"Error getting system data: {str(e)}")
            return {'error': str(e)}

    async def _get_trading_data(self) -> Dict[str, Any]:
        """Get trading data for live trading page."""
        try:
            trading_data = {
                'current_positions': [],
                'pending_orders': [],
                'recent_trades': [],
                'account_info': {}
            }

            # Get trading data from MT5 provider
            if hasattr(self, 'data_providers') and 'mt5_provider' in self.data_providers:
                provider = self.data_providers['mt5_provider']
                mt5_data = provider.get_data()
                trading_data.update(mt5_data)

            return trading_data

        except Exception as e:
            self.logger.error(f"Error getting trading data: {str(e)}")
            return {'error': str(e)}

    async def _get_performance_data(self) -> Dict[str, Any]:
        """Get performance data for performance page."""
        try:
            performance_data = {
                'model_performance': {},
                'prediction_accuracy': {},
                'ensemble_consensus': {},
                'performance_charts': {}
            }

            # Get performance data from model provider
            if hasattr(self, 'data_providers') and 'model_provider' in self.data_providers:
                provider = self.data_providers['model_provider']
                model_data = provider.get_data()
                performance_data.update(model_data)

            return performance_data

        except Exception as e:
            self.logger.error(f"Error getting performance data: {str(e)}")
            return {'error': str(e)}

    async def _get_system_status(self) -> Dict[str, Any]:
        """Get system status data."""
        return {
            'timestamp': datetime.now().isoformat(),
            'status': self.status.value,
            'mt5_connected': True,  # Will be updated with real data
            'models_loaded': True,
            'data_feed_active': True
        }

    async def _get_metrics_data(self) -> Dict[str, Any]:
        """Get metrics data."""
        return {
            'timestamp': datetime.now().isoformat(),
            'total_trades': 0,
            'win_rate': 0.0,
            'total_pnl': 0.0,
            'sharpe_ratio': 0.0
        }

    async def _get_positions_data(self) -> Dict[str, Any]:
        """Get positions data."""
        return {
            'timestamp': datetime.now().isoformat(),
            'positions': []
        }

    async def _get_signals_data(self) -> Dict[str, Any]:
        """Get signals data."""
        return {
            'timestamp': datetime.now().isoformat(),
            'signals': []
        }

    async def _get_results_data(self, result_type: str) -> Dict[str, Any]:
        """Get results data."""
        return {
            'timestamp': datetime.now().isoformat(),
            'result_type': result_type,
            'results': []
        }

    def start(self) -> bool:
        """Start the FastAPI server."""
        try:
            if not self.app:
                self.add_error("FastAPI app not initialized")
                return False

            self.logger.info(f"Starting FastAPI server on {self.config.host}:{self.config.port}")

            # Note: In production, this would be handled by the orchestrator
            # For now, we just mark as started
            self.status = DashboardStatus.RUNNING
            return True

        except Exception as e:
            self.add_error(f"Failed to start FastAPI server: {str(e)}")
            return False

    def stop(self) -> bool:
        """Stop the FastAPI server."""
        try:
            self.logger.info("Stopping FastAPI server...")
            self.status = DashboardStatus.STOPPED
            return True
        except Exception as e:
            self.add_error(f"Failed to stop FastAPI server: {str(e)}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """Get server status."""
        return {
            'status': self.status.value,
            'last_update': self.last_update,
            'host': self.config.host,
            'port': self.config.port,
            'app_initialized': self.app is not None
        }

    def set_websocket_handler(self, handler):
        """Set WebSocket handler for real-time updates."""
        self.websocket_handler = handler
        if handler and self.data_providers:
            handler.set_data_providers(self.data_providers)
        self.logger.info("WebSocket handler configured")

    def set_data_providers(self, providers):
        """Set data providers and update WebSocket handler."""
        self.data_providers.update(providers)
        if self.websocket_handler:
            self.websocket_handler.set_data_providers(self.data_providers)
        self.logger.info(f"Updated data providers: {list(providers.keys())}")


class WebSocketHandler(BaseDashboardComponent):
    """WebSocket handler for real-time updates."""

    def __init__(self, config, **kwargs):
        """Initialize WebSocket handler."""
        super().__init__(config)
        self.kwargs = kwargs
        self.connections: Set[WebSocket] = set()
        self.app = kwargs.get('app')  # FastAPI app instance

        # Data update task
        self.update_task = None
        self.is_broadcasting = False

    def initialize(self) -> bool:
        """Initialize the WebSocket handler."""
        try:
            if not FASTAPI_AVAILABLE:
                self.add_error("FastAPI not available for WebSocket support")
                return False

            self.logger.info("Initializing WebSocket handler...")

            if self.app:
                self._setup_websocket_routes()

            self.status = DashboardStatus.RUNNING
            self.logger.info("✓ WebSocket handler initialized successfully")
            return True

        except Exception as e:
            self.add_error(f"Failed to initialize WebSocket handler: {str(e)}")
            self.status = DashboardStatus.ERROR
            return False

    def _setup_websocket_routes(self):
        """Setup WebSocket routes."""

        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """Main WebSocket endpoint for real-time updates."""
            await self.connect(websocket)
            try:
                while True:
                    # Keep connection alive and handle incoming messages
                    data = await websocket.receive_text()

                    # Handle client messages (ping, requests, etc.)
                    await self._handle_client_message(websocket, data)

            except WebSocketDisconnect:
                await self.disconnect(websocket)
            except Exception as e:
                self.logger.error(f"WebSocket error: {str(e)}")
                await self.disconnect(websocket)

    async def connect(self, websocket: WebSocket):
        """Accept WebSocket connection."""
        try:
            await websocket.accept()
            self.connections.add(websocket)
            self.logger.info(f"WebSocket connected. Total connections: {len(self.connections)}")

            # Send initial data
            await self._send_initial_data(websocket)

        except Exception as e:
            self.logger.error(f"Error accepting WebSocket connection: {str(e)}")

    async def disconnect(self, websocket: WebSocket):
        """Handle WebSocket disconnection."""
        try:
            if websocket in self.connections:
                self.connections.remove(websocket)
                self.logger.info(f"WebSocket disconnected. Total connections: {len(self.connections)}")
        except Exception as e:
            self.logger.error(f"Error handling WebSocket disconnection: {str(e)}")

    async def _handle_client_message(self, websocket: WebSocket, message: str):
        """Handle incoming client messages."""
        try:
            data = json.loads(message)
            message_type = data.get('type', 'unknown')

            if message_type == 'ping':
                await websocket.send_text(json.dumps({'type': 'pong', 'timestamp': datetime.now().isoformat()}))

            elif message_type == 'request_data':
                data_type = data.get('data_type', 'all')
                await self._send_requested_data(websocket, data_type)

            else:
                self.logger.warning(f"Unknown WebSocket message type: {message_type}")

        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON in WebSocket message: {message}")
        except Exception as e:
            self.logger.error(f"Error handling WebSocket message: {str(e)}")

    async def _send_initial_data(self, websocket: WebSocket):
        """Send initial data to newly connected client."""
        try:
            initial_data = {
                'type': 'initial_data',
                'timestamp': datetime.now().isoformat(),
                'system_status': 'connected',
                'config': {
                    'update_interval': self.config.update_interval_seconds,
                    'theme': self.config.theme,
                    'title': self.config.dashboard_title
                }
            }

            await websocket.send_text(json.dumps(initial_data))

        except Exception as e:
            self.logger.error(f"Error sending initial data: {str(e)}")

    async def _send_requested_data(self, websocket: WebSocket, data_type: str):
        """Send requested data to client."""
        try:
            # This will be implemented with real data providers
            response_data = {
                'type': 'data_response',
                'data_type': data_type,
                'timestamp': datetime.now().isoformat(),
                'data': {}  # Will be populated with real data
            }

            await websocket.send_text(json.dumps(response_data))

        except Exception as e:
            self.logger.error(f"Error sending requested data: {str(e)}")

    async def broadcast_update(self, update_data: Dict[str, Any]):
        """Broadcast update to all connected clients."""
        if not self.connections:
            return

        try:
            message = json.dumps({
                'type': 'update',
                'timestamp': datetime.now().isoformat(),
                **update_data
            })

            # Send to all connections
            disconnected = set()
            for websocket in self.connections.copy():
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    self.logger.warning(f"Failed to send to WebSocket: {str(e)}")
                    disconnected.add(websocket)

            # Remove disconnected clients
            for websocket in disconnected:
                await self.disconnect(websocket)

        except Exception as e:
            self.logger.error(f"Error broadcasting update: {str(e)}")

    def start(self) -> bool:
        """Start the WebSocket handler."""
        try:
            self.logger.info("Starting WebSocket handler...")

            # Start periodic update task
            if not self.is_broadcasting:
                self.is_broadcasting = True
                # Note: In production, this would be managed by the orchestrator

            return True

        except Exception as e:
            self.add_error(f"Failed to start WebSocket handler: {str(e)}")
            return False

    def stop(self) -> bool:
        """Stop the WebSocket handler."""
        try:
            self.logger.info("Stopping WebSocket handler...")

            # Stop broadcasting
            self.is_broadcasting = False

            # Close all connections
            for websocket in self.connections.copy():
                asyncio.create_task(self.disconnect(websocket))

            self.status = DashboardStatus.STOPPED
            return True

        except Exception as e:
            self.add_error(f"Failed to stop WebSocket handler: {str(e)}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """Get handler status."""
        return {
            'status': self.status.value,
            'last_update': self.last_update,
            'active_connections': len(self.connections),
            'is_broadcasting': self.is_broadcasting
        }
