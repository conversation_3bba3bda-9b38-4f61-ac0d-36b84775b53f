<!DOCTYPE html>
<html lang="en" data-theme="{{ theme }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ title }}{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', path='/favicon.ico') }}">

    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'nasa-dark': '#0A0F1C',
                        'nasa-secondary': '#1A1F2E',
                        'nasa-accent': '#22C55E',
                        'nasa-red': '#EF4444',
                        'nasa-gray': '#64748B',
                        'nasa-light': '#F8FAFC'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'IBM Plex Sans', 'system-ui', 'sans-serif']
                    },
                    spacing: {
                        '18': '4.5rem',
                        '88': '22rem'
                    }
                }
            }
        }
    </script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

    <!-- Custom CSS -->
    <link href="{{ url_for('static', path='/css/dashboard.css') }}" rel="stylesheet">

    {% block extra_head %}{% endblock %}
</head>
<body class="bg-nasa-dark text-nasa-light font-sans antialiased">
    <!-- Sidebar -->
    <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-nasa-secondary border-r border-nasa-gray/20 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out">
        <!-- Sidebar Header -->
        <div class="flex items-center justify-between h-16 px-6 border-b border-nasa-gray/20">
            <div class="flex items-center">
                <i class="fas fa-chart-line text-nasa-accent text-xl mr-3"></i>
                <h1 class="text-lg font-semibold text-nasa-light">{{ title }}</h1>
            </div>
            <button id="sidebar-close" class="lg:hidden text-nasa-gray hover:text-nasa-light">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Navigation Links -->
        <nav class="mt-6 px-3">
            <div class="space-y-1">
                <a href="/" class="nav-link flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:bg-nasa-gray/10 hover:text-nasa-accent group">
                    <i class="fas fa-home mr-3 text-nasa-gray group-hover:text-nasa-accent"></i>
                    Dashboard
                </a>
                <a href="/trading" class="nav-link flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:bg-nasa-gray/10 hover:text-nasa-accent group">
                    <i class="fas fa-exchange-alt mr-3 text-nasa-gray group-hover:text-nasa-accent"></i>
                    Live Trading
                </a>
                <a href="/performance" class="nav-link flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:bg-nasa-gray/10 hover:text-nasa-accent group">
                    <i class="fas fa-chart-bar mr-3 text-nasa-gray group-hover:text-nasa-accent"></i>
                    Performance
                </a>
                <a href="/results" class="nav-link flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:bg-nasa-gray/10 hover:text-nasa-accent group">
                    <i class="fas fa-archive mr-3 text-nasa-gray group-hover:text-nasa-accent"></i>
                    Results
                </a>
                <a href="/system" class="nav-link flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:bg-nasa-gray/10 hover:text-nasa-accent group">
                    <i class="fas fa-cog mr-3 text-nasa-gray group-hover:text-nasa-accent"></i>
                    System
                </a>
            </div>
        </nav>

        <!-- Sidebar Footer -->
        <div class="absolute bottom-0 left-0 right-0 p-6 border-t border-nasa-gray/20">
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <i class="fas fa-circle text-nasa-accent mr-2" id="connection-status"></i>
                    <span id="connection-text" class="text-nasa-gray">Connected</span>
                </div>
                <div class="flex items-center text-nasa-gray">
                    <i class="fas fa-clock mr-1"></i>
                    <span id="current-time"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>

    <!-- Mobile Header -->
    <div class="lg:hidden bg-nasa-secondary border-b border-nasa-gray/20 px-4 py-3">
        <div class="flex items-center justify-between">
            <button id="sidebar-toggle" class="text-nasa-gray hover:text-nasa-light">
                <i class="fas fa-bars text-xl"></i>
            </button>
            <div class="flex items-center">
                <i class="fas fa-chart-line text-nasa-accent text-lg mr-2"></i>
                <h1 class="text-lg font-semibold text-nasa-light">{{ title }}</h1>
            </div>
            <div class="w-8"></div> <!-- Spacer for centering -->
        </div>
    </div>

    <!-- Main Content -->
    <main class="lg:ml-64 min-h-screen bg-nasa-dark">
        <div class="p-6 lg:p-8">
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Footer -->
    <footer class="lg:ml-64 bg-nasa-secondary border-t border-nasa-gray/20 px-6 lg:px-8 py-4">
        <div class="flex flex-col sm:flex-row justify-between items-center text-sm text-nasa-gray">
            <div class="mb-2 sm:mb-0">
                &copy; 2024 Apex v4 Trading System. All rights reserved.
            </div>
            <div>
                Last Update: <span id="last-update" class="text-nasa-light">{{ data.timestamp if data else 'Never' }}</span>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="{{ url_for('static', path='/js/dashboard.js') }}"></script>

    {% block extra_scripts %}{% endblock %}

    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar functionality
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebarClose = document.getElementById('sidebar-close');
            const sidebarOverlay = document.getElementById('sidebar-overlay');

            function openSidebar() {
                sidebar.classList.remove('-translate-x-full');
                sidebarOverlay.classList.remove('hidden');
            }

            function closeSidebar() {
                sidebar.classList.add('-translate-x-full');
                sidebarOverlay.classList.add('hidden');
            }

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', openSidebar);
            }

            if (sidebarClose) {
                sidebarClose.addEventListener('click', closeSidebar);
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', closeSidebar);
            }

            // Active navigation link highlighting
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath ||
                    (currentPath === '/' && link.getAttribute('href') === '/') ||
                    (currentPath.startsWith('/trading') && link.getAttribute('href') === '/trading') ||
                    (currentPath.startsWith('/performance') && link.getAttribute('href') === '/performance') ||
                    (currentPath.startsWith('/results') && link.getAttribute('href') === '/results') ||
                    (currentPath.startsWith('/system') && link.getAttribute('href') === '/system')) {
                    link.classList.add('bg-nasa-accent/10', 'text-nasa-accent');
                    link.querySelector('i').classList.add('text-nasa-accent');
                }
            });

            // Initialize WebSocket connection
            if (typeof DashboardWebSocket !== 'undefined') {
                window.dashboardWS = new DashboardWebSocket();
            }

            // Update current time
            function updateTime() {
                const now = new Date();
                const timeElement = document.getElementById('current-time');
                if (timeElement) {
                    timeElement.textContent = now.toLocaleTimeString();
                }
            }

            updateTime();
            setInterval(updateTime, 1000);

            // Initialize page-specific functionality
            if (typeof initializePage === 'function') {
                initializePage();
            }
        });
    </script>
</body>
</html>
