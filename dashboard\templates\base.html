<!DOCTYPE html>
<html lang="en" data-theme="{{ theme }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ title }}{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', path='/favicon.ico') }}">
    
    <!-- CSS -->
    <link href="{{ url_for('static', path='/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', path='/css/dashboard.css') }}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="dashboard-body">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                {{ title }}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/performance">
                            <i class="fas fa-chart-bar me-1"></i>Performance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/trading">
                            <i class="fas fa-exchange-alt me-1"></i>Live Trading
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/results">
                            <i class="fas fa-archive me-1"></i>Results
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/system">
                            <i class="fas fa-cog me-1"></i>System
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text me-3">
                            <i class="fas fa-circle text-success me-1" id="connection-status"></i>
                            <span id="connection-text">Connected</span>
                        </span>
                    </li>
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="fas fa-clock me-1"></i>
                            <span id="current-time"></span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            {% block content %}{% endblock %}
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="footer bg-dark text-light py-3 mt-auto">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <small>&copy; 2024 Apex v4 Trading System. All rights reserved.</small>
                </div>
                <div class="col-md-6 text-end">
                    <small>
                        Last Update: <span id="last-update">{{ data.timestamp if data else 'Never' }}</span>
                    </small>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', path='/js/dashboard.js') }}"></script>
    
    {% block extra_scripts %}{% endblock %}
    
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize WebSocket connection
            if (typeof DashboardWebSocket !== 'undefined') {
                window.dashboardWS = new DashboardWebSocket();
            }
            
            // Update current time
            function updateTime() {
                const now = new Date();
                document.getElementById('current-time').textContent = now.toLocaleTimeString();
            }
            
            updateTime();
            setInterval(updateTime, 1000);
            
            // Initialize page-specific functionality
            if (typeof initializePage === 'function') {
                initializePage();
            }
        });
    </script>
</body>
</html>
